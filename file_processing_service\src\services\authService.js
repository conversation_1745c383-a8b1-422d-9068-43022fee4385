const { ConfidentialClientApplication } = require('@azure/msal-node');
const logger = require('../utils/logger');

const msalConfig = {
  auth: {
    clientId: process.env.AZURE_CLIENT_ID,
    clientSecret: process.env.AZURE_CLIENT_SECRET,
    authority: `https://login.microsoftonline.com/${process.env.AZURE_TENANT_ID}`
  }
};

const pca = new ConfidentialClientApplication(msalConfig);

const appScopes = ['https://graph.microsoft.com/.default'];

async function getAppAccessToken() {
  try {
    logger.info('Acquiring access token using client credentials flow');
    
    const tokenResponse = await pca.acquireTokenByClientCredential({
      scopes: appScopes
    });
    
    if (!tokenResponse || !tokenResponse.accessToken) {
      throw new Error('Failed to acquire access token');
    }
    
    logger.info('Successfully acquired access token');
    return tokenResponse.accessToken;
  } catch (error) {
    logger.error(`Error acquiring access token: ${error.message}`);
    throw error;
  }
}

module.exports = {
  getAppAccessToken
}; 