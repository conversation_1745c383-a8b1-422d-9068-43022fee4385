// src/controllers/qcDigestController.js
const { getConnection } = require("./dbConnections");
const { loadSqlQueries } = require("./sqlLoader");
const sql = require("mssql");

const getBusinessUnitByPo = async (poNumber) => {
  try {
    console.log("poNumber", poNumber);
    const pool = await getConnection("DocProcessingDigest");
    const sqlQueries = await loadSqlQueries();
    const result = await pool
      .request()
      .input("PoNo", sql.VarChar, poNumber)
      .query(sqlQueries.getBusinessUnitByPo);
    return result.recordset[0];
  } catch (error) {
    console.error("Error in getBusinessUnitByPo", error);
    throw error;
  }
};

module.exports = { getBusinessUnitByPo };
