DECLARE @BusinessUnit NVARCHAR(50);

DECLARE @contactEmailAddress NVARCHAR(50);
DECLARE @InternalEmailAddress NVARCHAR(50);
DECLARE @CustomerAlertEmailAddress NVARCHAR(50);
DECLARE @CustomerAlertCCEmailAddress NVARCHAR(50);

SELECT
    @BusinessUnit = BusinessUnit
FROM
    GetBusinessUnitByPO
WHERE
    PoNo = @PoNo;

SELECT
    @contactEmailAddress = EmailAddress,
    @CustomerAlertEmailAddress = CustomerAlertEmailAddress,
    @CustomerAlertCCEmailAddress = CustomerAlertCCEmailAddress
FROM
    GetContactEmailAddressByPO
WHERE
    PoNo = @PoNo;

SELECT
    @InternalEmailAddress = EmailAddress
FROM
    GetInternalEmailAddressByPO
WHERE
    PoNo = @PoNo;

UPDATE
    dbo.Digests
SET
    PoNo = @PoNo,
    ExternalPoNo = @ISSPO,
    SupplierCode = @supplierCode,
    InspectionDate = @inspection_date,
    BusinessUnit = @BusinessUnit,
    CustomerName = @endCustomer,
    emailAddress = @contactEmailAddress,
    SupplierName = @supplier,
    Country = @coo,
    internalEmailAddress = @InternalEmailAddress,
    CustomerAlertEmailAddress = @CustomerAlertEmailAddress,
    CustomerAlertCCEmailAddress = @CustomerAlertCCEmailAddress
WHERE
    id = @DigestID;

UPDATE
    dbo.DigestFiles
SET
    PoNo = @PoNo,
    ExternalPoNo = @ISSPO,
    SupplierCode = @supplierCode,
    InspectionDate = @inspection_date,
    ProcessingStatus = @Status,
    SupplierName = @supplier
WHERE
DigestID = @DigestID;