const fs = require("fs").promises;
const { spawn } = require('child_process');
const path = require('path');
const logger = require("../utils/logger");
const {
  sendTeamsNotification
} = require("../utils/notificationService");
const performOCR = async (filePath, digestId) => {
  try {
    logger.info(`Starting OCR processing for: ${filePath}`, {
      digestId: digestId,
    });

    // Validate file existence
    if (!(await fs.access(filePath).then(() => true).catch(() => false))) {
      throw new Error(`File not found at path: ${filePath}`);
    }

    // Validate file path to prevent command injection
    const normalizedPath = path.normalize(filePath);
    if (normalizedPath.includes('..')) {
      throw new Error('Invalid file path detected');
    }

    // Define the path to the OCR executable
    const exePath = path.join(process.env.OCR_EXE_PATH || 'C:\\Users\\<USER>\\source\\repos\\leedtools\\bin\\Debug\\net8.0\\leedtools.exe');
    logger.info(`Attempting to execute: ${exePath}`);
    logger.info(`Current working directory: ${process.cwd()}`);

    // Execute the OCR process
    const result = await new Promise((resolve, reject) => {
      const ocrProcess = spawn(exePath, [normalizedPath]);

      let stdoutData = '';
      let stderrData = '';

      ocrProcess.stdout.on('data', (data) => {
        stdoutData += data.toString();
      });

      ocrProcess.stderr.on('data', (data) => {
        stderrData += data.toString();
      });

      ocrProcess.on('close', (code) => {
        if (code === 0) {
          // Extract just the file path from stdout (first line)
          const jsonFilePath = stdoutData.split('\r\n')[0].trim();
          logger.info(`Extracted JSON file path: ${jsonFilePath}`);
          resolve({ success: true, jsonFilePath });
        } else {
          reject(new Error(`OCR process failed with code ${code}. Error: ${stderrData}`));
        }
      });

      ocrProcess.on('error', (error) => {
        reject(new Error(`Failed to start OCR process: ${error.message}`));
      });

      setTimeout(() => {
        ocrProcess.kill();
        reject(new Error('OCR process timed out after 5 minutes'));
      }, 300000);
    });

    // Read and parse the output JSON file
    let parsedData = {};
    const jsonFilePath = result.jsonFilePath; // This will now be clean, without the evaluation notice
    
    logger.info(`Attempting to read JSON from: ${jsonFilePath}`);
    
    if (await fs.access(jsonFilePath).then(() => true).catch(() => false)) {
      const jsonData = await fs.readFile(jsonFilePath, "utf8");
      parsedData = JSON.parse(jsonData);
      logger.info('Successfully parsed OCR output data', { digestId: digestId });
    } else {
      throw new Error(`OCR output file not found at: ${jsonFilePath}`);
    }

    return { parsedData, jsonFilePath };

  } catch (error) {
    logger.error(
      `Error during OCR processing for file ${filePath}: ${error.message}`
    );

    // Send Teams notification about the failure
    try {
      await sendTeamsNotification("OCR Processing Failed", "ERROR", {
        status: "Failed",
        error: error.message,
        additionalInfo: {
          "File Path": filePath,
          "Error Type": error.name,
          "Stack Trace": error.stack
        },
      }).catch((notifyErr) => {
        logger.error(
          `Failed to send error notification to Teams: ${notifyErr.message}`
        );
      });
    } catch (teamsError) {
      logger.error(`Error sending Teams notification: ${teamsError.message}`);
    }

    throw error;
  }
};

module.exports = { performOCR };
