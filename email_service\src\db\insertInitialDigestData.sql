DECLARE @NewDigestID INT,
        @NewDigestFileID INT;

-- Insert into Digests Table
INSERT INTO [dbo].[Digests] (
    ImportID,
    MessageID,
    ProcessingStatus,
    IsArchived,
    Business_id,
    OriginatingSenderEmailAddress
)
VALUES (
    NEWID(),
    @msgId,
    @processingStatus,
    0,
    4,
    @senderEmail
);

-- Get the ID of the newly inserted record
SET @NewDigestID = SCOPE_IDENTITY();

PRINT 'New Digest ID: ' + CAST(@NewDigestID AS NVARCHAR);

select @NewDigestID as DigestID;
