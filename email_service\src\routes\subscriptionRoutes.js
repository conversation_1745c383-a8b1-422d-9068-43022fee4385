const express = require('express');
const router = express.Router();
const { deleteAllSubscriptions } = require('../services/subscriptionService');
const logger = require('../utils/logger');

router.get('/deleteAllSubscriptions', async (req, res) => {
  try {
    const subscriptions = await deleteAllSubscriptions();
    res.status(200).json(subscriptions);
  } catch (error) {
    logger.error('Error listing subscriptions:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router; 