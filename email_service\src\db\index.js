// src/controllers/qcDigestController.js
const { getConnection } = require("./dbConnections");
const { loadSqlQueries } = require("./sqlLoader");
const sql = require("mssql");

const createQCDigestData = async (messageId, senderEmail,processingStatus=0) => {
  console.log("creating qc digest data",processingStatus);
  try {
    const sqlQueries = await loadSqlQueries();
    const pool = await getConnection("DocProcessingDigest");

    const result = await pool
      .request()
      .input("msgId", sql.NVarChar, messageId)
      .input("senderEmail", sql.NVarChar, senderEmail)
      .input("processingStatus", sql.Int, processingStatus)
      .query(sqlQueries.insertInitialDigestData);
    console.log("result from qc digest data", result);
    const response = result.recordset.length > 0 ? result.recordset[0] : {};
    return response;
  } catch (err) {
    console.error("Error inserting qc digest data: ", err);
    return "Error running insert script";
  }
};
const createQCDigestFileData = async (data) => {
  try {
    console.log("Data recieved to insert", data);
    const sqlQueries = await loadSqlQueries();
    const pool = await getConnection("DocProcessingDigest");

    const result = await pool
      .request()
      .input("digestID", sql.NVarChar, String(data.digestId))
      .input("fileName", sql.NVarChar, data.fileName)
      .input("typeOfDocument", sql.Int, data.typeOfDocument)
      .query(sqlQueries.insertDigestsFileAndPallet);
    console.log("result", result);
    return {
      message: "Data inserted successfully",
    };
  } catch (err) {
    console.error("Error inserting qc digest file data: ", err);
    return "Error running insert script";
  }
};

module.exports = {
  createQCDigestData,
  createQCDigestFileData,
};
