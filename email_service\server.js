const app = require("./src/app");
const { connectionPools, closeAllConnections } = require("./src/db/dbConnections");
const logger = require("./src/utils/logger");
const { emailProcessingWorker } = require('./src/workers/queueWorkers');
const { cleanupWorker } = require('./src/workers/cleanupWorker');
const { scheduleRenewalJob } = require('./src/services/scheduledJobs');
const handleFatalErrors = require("./src/utils/handleFatalError");

scheduleRenewalJob(); 
const PORT = process.env.PORT || 8008;

const server = app.listen(PORT, () => {
  logger.info(` Server running on http://localhost:${PORT}`);
});


console.log("connection pools",connectionPools)
const shutdown = async (signal) => {
  logger.warn(`${signal} received. Closing server...`);
  try {
    await Promise.all([
      emailProcessingWorker.close(),
      cleanupWorker.close(),
      closeAllConnections()
    ]);
    
    server.close(() => {
      logger.info("HTTP server closed.");
      process.exit(0);
    });
  } catch (err) {
    await handleFatalErrors(err);
    logger.error("Error during shutdown: "+err.message);
    process.exit(1);
  }
};

process.on("SIGINT", () => shutdown("SIGINT"));
process.on("SIGTERM", () => shutdown("SIGTERM"));

process.on("uncaughtException", (err) => {
  logger.error("Uncaught Exception: "+err.message);
  process.exit(1);
});

process.on("unhandledRejection", (reason) => {
  logger.error("Unhandled Promise Rejection: "+reason);
});
