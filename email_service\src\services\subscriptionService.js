const axios = require('axios');
const logger = require('../utils/logger');
const { getAppAccessToken } = require('./authService');

async function createSubscription(email) {
  try {
    logger.info(`Creating subscription for mailbox: ${email}`);
    
    // Get application access token
    const accessToken = await getAppAccessToken();
    
    // Check if WEBHOOK_URL is set
    if (!process.env.WEBHOOK_URL) {
      logger.error('WEBHOOK_URL environment variable is not set');
      throw new Error('WEBHOOK_URL environment variable is not set');
    }
    
    const subscriptionData = {
      changeType: "created",
      notificationUrl: process.env.WEBHOOK_URL,
      resource: `users/${email}/mailFolders('inbox')/messages`,
      expirationDateTime: new Date(Date.now() + 4230 * 60000).toISOString(),
      clientState: process.env.CLIENT_STATE || "secretClientState"
    };
    
    logger.info(`Subscription data: ${JSON.stringify(subscriptionData)}`);
    console.log("subscriptionData", subscriptionData);
    const response = await axios.post(
      'https://graph.microsoft.com/v1.0/subscriptions',
      subscriptionData,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    logger.info(`Successfully created subscription for mailbox: ${email}`);
    return response.data;
  } catch (error) {
    logger.error(`Error creating subscription: ${error}`);
    // Log the full error response for debugging
    if (error.response && error.response.data) {
      logger.error(`Error response data: ${JSON.stringify(error.response.data)}`);
    }
    logger.error(`Error creating subscription: ${error.message}`);
    throw error;
  }
}

async function listSubscriptions() {
  try {
    logger.info('Listing all subscriptions');
    // Get application access token
    const accessToken = await getAppAccessToken();
    
    const response = await axios.get(
      'https://graph.microsoft.com/v1.0/subscriptions',
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    logger.info(`Found ${response.data.value.length} subscriptions`);
    return response.data.value;
  } catch (error) {
    logger.error(`Error listing subscriptions: ${error.message}`);
    throw error;
  }
}

async function renewSubscription(subscriptionId) {
  try {
    logger.info(`Renewing subscription: ${subscriptionId}`);
    
    // Get application access token
    const accessToken = await getAppAccessToken();
    
    const renewalData = {
      expirationDateTime: new Date(Date.now() + 4230 * 60000).toISOString()
    };
    
    const response = await axios.patch(
      `https://graph.microsoft.com/v1.0/subscriptions/${subscriptionId}`,
      renewalData,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    logger.info(`Successfully renewed subscription: ${subscriptionId}`);
    return response.data;
  } catch (error) {
    logger.error(`Error renewing subscription: ${error.message}`);
    throw error;
  }
}

async function deleteSubscription(subscriptionId) {
  try {
    logger.info(`Deleting subscription: ${subscriptionId}`);
    
    // Get application access token
    const accessToken = await getAppAccessToken();
    
    await axios.delete(
      `https://graph.microsoft.com/v1.0/subscriptions/${subscriptionId}`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    logger.info(`Successfully deleted subscription: ${subscriptionId}`);
    return true;
  } catch (error) {
    if (error.response && error.response.data) {
      logger.error(`Error response data: ${JSON.stringify(error.response.data)}`);
    }
    logger.error(`Error deleting subscription: ${error.message}`);
    throw error;
  }
}

async function deleteAllSubscriptions() {
  try {
    logger.info('Deleting all subscriptions');
    
    // Get app access token
    const accessToken = await getAppAccessToken();
    
    // Get all current subscriptions
    const subscriptions = await listSubscriptions();
    
    if (subscriptions.length === 0) {
      logger.info('No subscriptions found to delete');
      return { deleted: 0 };
    }
    
    const results = {
      deleted: [],
      failed: []
    };
    
    // Delete each subscription
    for (const subscription of subscriptions) {
      try {
        await deleteSubscription(subscription.id);
        results.deleted.push(subscription.id);
      } catch (error) {
        results.failed.push({
          id: subscription.id,
          error: error.message
        });
      }
    }
    
    logger.info(`Deleted ${results.deleted.length} subscriptions, Failed: ${results.failed.length}`);
    return results;
  } catch (error) {
    logger.error(`Error deleting all subscriptions: ${error.message}`);
    throw error;
  }
}

module.exports = {
  createSubscription,
  listSubscriptions,
  renewSubscription,
  deleteSubscription,
  deleteAllSubscriptions,
}; 