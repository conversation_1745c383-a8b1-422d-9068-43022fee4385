const cron = require("node-cron");
const { checkForAwaitingIdentification } = require("../db/index");
const logger = require('../utils/logger');

async function processAwaitingFiles() {
  console.log('Running file check now...');
  logger.info('Checking for files awaiting identification...');
  try {
    const files = await checkForAwaitingIdentification();
    console.log(`Found ${files ? files.length : 0} files to process`);
  } catch (error) {
    console.error('Error in job scheduler:', error);
    logger.error('Error in job scheduler:', error);
  }
}

const startJobScheduler = async () => {
  console.log("STARTING JOB SCHEDULER - INITIALIZATION");
  try {
    const job = cron.schedule('*/5 * * * *', processAwaitingFiles);
    console.log("Cron job scheduled successfully");
    
    logger.info('Job scheduler started successfully - running every 5 minutes');
    
    console.log('Running initial job immediately');
    await processAwaitingFiles();
    
    return job; 
  } catch (error) {
    console.error('Failed to start job scheduler:', error);
    logger.error('Failed to start job scheduler:', error);
    throw error;
  }
};

module.exports = { startJobScheduler };
