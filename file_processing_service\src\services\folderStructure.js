const axios = require("axios");
const { retryOperation } = require("../utils/retry");
const logger = require("../utils/logger");
const { getSiteId } = require("./sharePointService");

async function createFolderStructures(
  poNumber,
  supplierCode,
  endCustomer,
  category,
  inspectionDate,
  accessToken,
  documentType = 1,
  businessName,
  digestId
) {
  console.log(
    "createFolderStructures",
    poNumber,
    supplierCode,
    endCustomer,
    category,
    inspectionDate,
    accessToken,
    documentType,
    businessName,
    digestId
  );
  try {
    let date;
    try {
      date = new Date(inspectionDate);
      
      if (isNaN(date.getTime())) {
        logger.warn(`Invalid inspection date: ${inspectionDate}, using current date`,{digestId:digestId});
        date = new Date();
      }
    } catch (error) {
      logger.warn(`Error parsing inspection date: ${error.message}, using current date`,{digestId:digestId});
      date = new Date();
    }

    const year = date.getFullYear().toString();
    const month = getMonthName(date.getMonth());
    const poDate = `${poNumber} - ${formatDate(date)}`;

    logger.info(
      `Creating folder structures for: SupplierCode=${supplierCode}, EndCustomer=${endCustomer}, Category=${category}, PO=${poNumber}, Date=${formatDate(date)}, DocumentType=${documentType}, BusinessName=${businessName}`,{digestId:digestId}
    );

    const targetFolder = documentType === 2 ? "SensorReadings" : "QCIntake";

    const supplierPath = await createSupplierPath(
      supplierCode,
      endCustomer,
      category,
      year,
      month,
      poDate,
      accessToken,
      targetFolder,
      digestId
    );

    const customerPath = await createCustomerPath(
      supplierCode,
      year,
      month,
      poDate,
      accessToken,
      targetFolder,
      businessName,
      digestId
    );

    return {
      supplierPath: supplierPath.path,
      supplierPathId: supplierPath.id,
      customerPath: customerPath.path,
      customerPathId: customerPath.id,
    };
  } catch (error) {
    logger.error(`Failed to create folder structures: ${error.message}`,{digestId:digestId});
    throw error;
  }
}

async function createSupplierPath(
  supplierCode,
  endCustomer,
  category,
  year,
  month,
  poDate,
  accessToken,
  targetFolder,
  digestId
) {
  await createOrGetFolder("", "Suppliers", accessToken,digestId);
  await createOrGetFolder("Suppliers", supplierCode, accessToken,digestId);
  await createOrGetFolder(
    `Suppliers/${supplierCode}`,
    "QC Documents",
    accessToken,
    digestId
  );
  await createOrGetFolder(
    `Suppliers/${supplierCode}/QC Documents`,
    endCustomer,
    accessToken,
    digestId
  );
  await createOrGetFolder(
    `Suppliers/${supplierCode}/QC Documents/${endCustomer}`,
    category,
    accessToken,
    digestId
  );
  await createOrGetFolder(
    `Suppliers/${supplierCode}/QC Documents/${endCustomer}/${category}`,
    year,
    accessToken,
    digestId
  );
  await createOrGetFolder(
    `Suppliers/${supplierCode}/QC Documents/${endCustomer}/${category}/${year}`,
    month,
    accessToken,
    digestId
  );
  await createOrGetFolder(
    `Suppliers/${supplierCode}/QC Documents/${endCustomer}/${category}/${year}/${month}`,
    poDate,
    accessToken,
    digestId
  );
  // Add target folder (QCIntake or SensorReadings)
  const targetFolderObj = await createOrGetFolder(
    `Suppliers/${supplierCode}/QC Documents/${endCustomer}/${category}/${year}/${month}/${poDate}`,
    targetFolder,
    accessToken,
    digestId
  );

  const path = `Suppliers/${supplierCode}/QC Documents/${endCustomer}/${category}/${year}/${month}/${poDate}/${targetFolder}`;
  logger.info(`Successfully created supplier folder structure: ${path}`,{digestId:digestId});

  return {
    path,
    id: targetFolderObj.id,
  };
}

async function createCustomerPath(
  supplierCode,
  year,
  month,
  poDate,
  accessToken,
  targetFolder,
  businessName,
  digestId
) {
  const customer = businessName;
  const product = "Cherry";

  await createOrGetFolder("", "Customers", accessToken,digestId);
  await createOrGetFolder("Customers", customer, accessToken,digestId);
  await createOrGetFolder(`Customers/${customer}`, "QC Documents", accessToken,digestId);
  await createOrGetFolder(
    `Customers/${customer}/QC Documents`,
    supplierCode,
    accessToken,
    digestId
  );
  await createOrGetFolder(
    `Customers/${customer}/QC Documents/${supplierCode}`,
    product,
    accessToken,
    digestId
  );
  await createOrGetFolder(
    `Customers/${customer}/QC Documents/${supplierCode}/${product}`,
    year,
    accessToken,
    digestId
  );
  await createOrGetFolder(
    `Customers/${customer}/QC Documents/${supplierCode}/${product}/${year}`,
    month,
    accessToken,
    digestId
  );
  await createOrGetFolder(
    `Customers/${customer}/QC Documents/${supplierCode}/${product}/${year}/${month}`,
    poDate,
    accessToken,
    digestId
  );
  // Add target folder (QCIntake or SensorReadings)
  const targetFolderObj = await createOrGetFolder(
    `Customers/${customer}/QC Documents/${supplierCode}/${product}/${year}/${month}/${poDate}`,
    targetFolder,
    accessToken,
    digestId
  );

  const path = `Customers/${customer}/QC Documents/${supplierCode}/${product}/${year}/${month}/${poDate}/${targetFolder}`;
  logger.info(`Successfully created customer folder structure: ${path}`,{digestId:digestId});

  return {
    path,
    id: targetFolderObj.id,
  };
}

async function createOrGetFolder(parentPath, folderName, accessToken,digestId) {
  return await retryOperation(async () => {
    try {
      const displayPath = parentPath
        ? `${parentPath}/${folderName}`
        : folderName;
      logger.info(`Creating or getting folder: ${displayPath}`,{digestId:digestId});

      let siteId = await getSiteId(accessToken);

      let apiEndpoint;
      if (!parentPath) {
        apiEndpoint = `https://graph.microsoft.com/v1.0/sites/${siteId}/drive/root/children`;
      } else {
        const formattedParentPath = parentPath.startsWith("/")
          ? parentPath.substring(1)
          : parentPath;
        apiEndpoint = `https://graph.microsoft.com/v1.0/sites/${siteId}/drive/items/root:/${formattedParentPath}:/children`;
      }

      const createResponse = await axios.post(
        apiEndpoint,
        {
          name: folderName,
          folder: {},
          "@microsoft.graph.conflictBehavior": "replace",
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      logger.info(`Successfully created or found folder: ${displayPath}`,{digestId:digestId});
      return createResponse.data;
    } catch (error) {
      console.log("error creating/getting folder****************", error);
      logger.error(
        `Error creating/getting folder ${parentPath}/${folderName}: ${error.message}`,{digestId:digestId}
      );

      if (
        error.response &&
        (error.response.status === 409 || error.response.status === 400)
      ) {
        logger.info(
          `Trying to get existing folder: ${parentPath}/${folderName}`,{digestId:digestId}
        );

        let siteId = await getSiteId(accessToken);

        const formattedPath = `${parentPath}/${folderName}`;
        const cleanPath = formattedPath.startsWith("/")
          ? formattedPath.substring(1)
          : formattedPath;

        const getResponse = await axios.get(
          `https://graph.microsoft.com/v1.0/sites/${siteId}/drive/items/root:/${cleanPath}`,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "Content-Type": "application/json",
            },
          }
        );

        logger.info(
          `Successfully retrieved existing folder: ${parentPath}/${folderName}`,{digestId:digestId}
        );
        return getResponse.data;
      }

      throw error;
    }
  }, `createOrGetFolder-${folderName}`);
}

/**
 * Uploads a file to the specified folder in OneDrive
 */
async function uploadFileToFolder(
  folderPath,
  fileName,
  contentBytes,
  accessToken,
  digestId
) {
  return await retryOperation(async () => {
    try {

      let siteId = await getSiteId(accessToken);

      const formattedPath = folderPath.startsWith("/")
        ? folderPath.substring(1)
        : folderPath;

      const uploadUrl = `https://graph.microsoft.com/v1.0/sites/${siteId}/drive/items/root:/${formattedPath}/${fileName}:/content`;

      const response = await axios.put(
        uploadUrl,
        Buffer.from(contentBytes, "base64"),
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/octet-stream",
          },
        }
      );

      logger.info(`Successfully uploaded ${fileName} to ${folderPath}`,{digestId:digestId});
      return response.data;
    } catch (error) {
      logger.error(
        `Failed to upload ${fileName} to ${folderPath}: ${error.message}`,{digestId:digestId}
      );
      throw error;
    }
  }, `uploadFile-${fileName}`);
}

function getMonthName(monthIndex) {
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];
  return months[monthIndex];
}

function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
}

module.exports = {
  createFolderStructures,
  uploadFileToFolder,
};
