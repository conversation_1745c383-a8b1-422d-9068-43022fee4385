const sql = require('mssql');

const dbConfigs = {
  DocProcessingDigest: {
    server: 'localhost\\SQLEXPRESS',
    database: 'DocProcessingDigest',
    options: {
      encrypt: false,
      enableArithAbort: true,
      port: 1433,
      trustServerCertificate: true
    },
    authentication: {
      type: 'default',
      options: {
        userName: 'sagar', // Replace with your SQL Server username
        password: 'admin' // Replace with your SQL Server password
      }
    }
  }
};

const connectionPools = {};

const getConnection = async (dbName) => {
  try {

    // Check if a pool already exists
    if (connectionPools[dbName]) {
      // If an existing pool is found but it's not connected, close and recreate it
      if (!connectionPools[dbName]._connected) {
        console.log(`Recreating pool for ${dbName}`);
        await connectionPools[dbName].close();
        delete connectionPools[dbName];
      }
    }

    // If no pool exists for the requested DB, create a new one
    if (!connectionPools[dbName]) {
      console.log(`Creating new pool for ${dbName}`);
      const pool = new sql.ConnectionPool(dbConfigs[dbName]);
      connectionPools[dbName] = await pool.connect();
      console.log(`Connected to ${dbName}`);
    }

    return connectionPools[dbName];
  } catch (err) {
    console.error(`Database Connection Failed for ${dbName}: `, err);
    throw err;
  }
};


module.exports = { getConnection };