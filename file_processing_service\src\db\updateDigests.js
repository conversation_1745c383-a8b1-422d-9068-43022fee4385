// src/controllers/qcDigestController.js
const { getConnection } = require("./dbConnections");
const { loadSqlQueries } = require("./sqlLoader");
const sql = require("mssql");

const updateDigestsData = async (file, status, ocrData) => {
  try {
    // console.log("ocr data", ocrData);
    // console.log("filename", file);
    console.log("status", status);
    const sqlQueries = await loadSqlQueries();
    const pool = await getConnection("DocProcessingDigest");
    const tableData = ocrData.pages
      .filter(
        (page) =>
          page.type === "Report" && page.content.includes("ISS Pallet ID")
      )
      .map((page) => page.data);

    // console.log("Extracted Table Data:", tableData);

    const updateResult = await pool
      .request()
      .input("DigestID", sql.NVarChar, String(file.DigestID)) // Convert to string
      .input("ID", sql.NVarChar, String(file.ID)) // Convert to string
      .input("Status", sql.Int, parseInt(status)) // Convert to integer
      .input("endCustomer", sql.NVarChar, String(tableData[0].end_customer))
      .input("PoNo", sql.NVarChar, String(tableData[0].customer_po))
      .input("ISSPO", sql.NVarChar, String(tableData[0].iss_po))
      .input("supplierCode", sql.NVarChar, String(tableData[0].supplier_code))
      .input("coo", sql.NVarChar, String(tableData[0].coo))
      .input("inspection_date", sql.DateTime, tableData[0].inspection_date)
      .input("supplier", sql.NVarChar, String(tableData[0].supplier))
      .input("rag", sql.NVarChar, String(tableData[0].rag_value))
      .input(
        "customerPalletId",
        sql.NVarChar,
        String(tableData[0].customer_pallet_id)
      )
      .query(sqlQueries.updateDigestData);
    tableData.map(async (data, index) => {
      const updateResult = await pool
        .request()
        .input("DigestID", sql.Int, file.DigestID) // Convert to string
        .input("ID", sql.Int, file.ID) // Convert to string
        .input("rag", sql.NVarChar, String(data.rag_value))
        .input(
          "customerPalletId",
          sql.NVarChar,
          String(data.customer_pallet_id)
        )
        .query(sqlQueries.createDigestPalletData);
    });

    // console.log(`File status updated: ${updateResult}`);
    return { message: "Success" };
  } catch (err) {
    console.error(`Error updating file status`, err);
    return { message: "Failed" };
  }
};

const updateDigestProcessingStatus = async (digestId, status) => {
  try {
    const sqlQueries = await loadSqlQueries();
    const pool = await getConnection("DocProcessingDigest");
    const updateResult = await pool
      .request()
      .input("DigestID", sql.Int, digestId)
      .input("Status", sql.Int, status)
      .query(sqlQueries.updateDigestProcessingStatus);
    return { message: "Success" };
  } catch (err) {
    console.error(`Error updating digest processing status`, err);
    return { message: "Failed" };
  }
};
module.exports = { updateDigestsData, updateDigestProcessingStatus };
