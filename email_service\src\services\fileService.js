const axios = require("axios");
const logger = require("../utils/logger");
const { sendEmailNotification } = require("./notificationService");

async function getSiteId(accessToken) {
  try {
    const siteUrl = process.env.SHAREPOINT_SITE_URL;
    logger.info(`Getting site ID for: ${siteUrl}`);

    // Parse the URL
    const urlObj = new URL(siteUrl);
    const hostname = urlObj.hostname;
    const pathParts = urlObj.pathname.split("/").filter(Boolean);
    const siteName = pathParts[1];
    const tenantName = hostname.split(".")[0];

    // Try different formats
    const formats = [
      // Format 1: hostname:/path
      `https://graph.microsoft.com/v1.0/sites/${hostname}:/sites/${siteName}`,

      // Format 2: tenant.sharepoint.com:/sites/siteName
      `https://graph.microsoft.com/v1.0/sites/${tenantName}.sharepoint.com:/sites/${siteName}`,
    ];

    let lastError = null;

    // Try each format until one works
    for (const endpoint of formats) {
      try {
        logger.info(`Trying endpoint: ${endpoint}`);

        const response = await axios.get(endpoint, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
        });

        logger.info(`Successfully retrieved site ID using: ${endpoint}`);
        return response.data.id;
      } catch (error) {
        lastError = error;
        logger.warn(`Failed with endpoint ${endpoint}: ${error.message}`);
        // Continue to the next format
      }
    }

    // If we get here, all formats failed
    throw lastError || new Error("All site ID lookup methods failed");
  } catch (error) {
    // Log the full error response for debugging
    if (error.response && error.response.data) {
      logger.error(
        `Error response data: ${JSON.stringify(error.response.data)}`
      );
    }
    logger.error(`Error getting site ID: ${error.message}`);
    throw error;
  }
}

async function uploadToSharePoint(
  fileName,
  contentBytes,
  accessToken,
  digestId,
  folderPath = "/Attachments"
) {
  try {
    let targetFolderPath = folderPath;
    if (digestId) {
      // Add digestId subfolder to the path
      targetFolderPath = `${folderPath}/${digestId}`;
      logger.info(`Using digest-specific folder path: ${targetFolderPath}`);
    }
    logger.info(
      `Uploading file to SharePoint: ${fileName} in folder: ${targetFolderPath}`
    );

    // Get site ID
    const siteId = await getSiteId(accessToken);

    // Ensure folder path exists
    await createFolderPath(targetFolderPath, accessToken);

    // Convert base64 to buffer
    const fileBuffer = Buffer.from(contentBytes, "base64");

    // Upload file
    const uploadResponse = await axios.put(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/drive/root:${targetFolderPath}/${fileName}:/content`,
      fileBuffer,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/octet-stream",
        },
      }
    );

    logger.info(`Successfully uploaded file: ${fileName} to SharePoint`);
    return uploadResponse.data;
  } catch (error) {
    logger.error(`Error uploading file to SharePoint: ${error.message}`);
    throw error;
  }
}

async function createFolderPath(folderPath, accessToken) {
  try {
    logger.info(`Creating folder path: ${folderPath}`);

    // Get site ID
    const siteId = await getSiteId(accessToken);

    // Split path into segments
    const segments = folderPath.split("/").filter((segment) => segment);

    let currentPath = "/Shared Documents";
    let folderInfo = null;

    // Create each folder in the path if it doesn't exist
    for (const segment of segments) {
      try {
        // Check if folder exists
        const checkResponse = await axios.get(
          `https://graph.microsoft.com/v1.0/sites/${siteId}/drive/root:${currentPath}/${segment}`,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "Content-Type": "application/json",
            },
          }
        );

        folderInfo = checkResponse.data;
      } catch (error) {
        // Folder doesn't exist, create it
        if (error.response && error.response.status === 404) {
          logger.info(`Creating folder: ${segment} in ${currentPath}`);

          const createResponse = await axios.post(
            `https://graph.microsoft.com/v1.0/sites/${siteId}/drive/root:${currentPath}:/children`,
            {
              name: segment,
              folder: {},
              "@microsoft.graph.conflictBehavior": "rename",
            },
            {
              headers: {
                Authorization: `Bearer ${accessToken}`,
                "Content-Type": "application/json",
              },
            }
          );

          folderInfo = createResponse.data;
        } else {
          throw error;
        }
      }

      // Update current path
      currentPath = `${currentPath}/${segment}`;
    }

    logger.info(`Successfully created folder path: ${folderPath}`);
    return folderInfo;
  } catch (error) {
    logger.error(`Error creating folder path: ${error.message}`);
    throw error;
  }
}

function getDocumentType(attachment) {
  const fileName = attachment.fileName.toLowerCase();

  if (fileName.endsWith(".pdf")) {
    // Check if it's a PO PDF
    if (fileName.toLowerCase().startsWith("po")) {
      return 1; // PO_PDF
    }
    return 2; // Regular PDF
  } else if (
    fileName.endsWith(".jpg") ||
    fileName.endsWith(".jpeg") ||
    fileName.endsWith(".png")
  ) {
    return 3; // Image
  }
}

// Add this new function to check for invalid SharePoint characters
function isValidSharePointFileName(fileName) {
  const invalidChars = /[~"#%&*:<>?/\\{|}]/;
  const maxLength = 128;

  if (invalidChars.test(fileName)) {
    return {
      isValid: false,
      reason: `File name contains invalid characters: ${fileName}`,
    };
  }

  if (fileName.length > maxLength) {
    return {
      isValid: false,
      reason: `File name exceeds maximum length of ${maxLength} characters: ${fileName}`,
    };
  }

  return {
    isValid: true,
  };
}

// Modify the getEmailAttachments function to check filenames
async function getEmailAttachments(
  messageId,
  accessToken,
  mailboxEmailId,
  senderEmail
) {
  try {
    logger.info(
      `Fetching attachments for email: ${messageId} from mailbox: ${mailboxEmailId}`
    );

    const response = await axios.get(
      `https://graph.microsoft.com/v1.0/users/${mailboxEmailId}/messages/${messageId}/attachments`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    const attachments = response.data.value;
    const supportedFileTypes = ["pdf", "jpg", "jpeg", "png"];
    let poPdfFound = false;

    // Check all attachments for invalid filenames first
    const invalidFiles = [];
    for (const attachment of attachments) {
      if (
        attachment["@odata.type"] === "#microsoft.graph.fileAttachment" &&
        !attachment.isInline
      ) {
        const fileExtension = attachment.name.split(".").pop().toLowerCase();

        if (supportedFileTypes.includes(fileExtension)) {
          const fileNameCheck = isValidSharePointFileName(attachment.name);
          if (!fileNameCheck.isValid) {
            invalidFiles.push({
              fileName: attachment.name,
              reason: fileNameCheck.reason,
            });
          }
        }
      }
    }

    // If any invalid files were found, stop processing and notify
    if (invalidFiles.length > 0) {
      const invalidFilesList = invalidFiles
        .map((file) => `- ${file.fileName}: ${file.reason}`)
        .join("\n");

      const errorMessage = `Email processing stopped due to invalid SharePoint filenames:\n${invalidFilesList}`;

      logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    // Continue with normal processing if all filenames are valid
    const downloadedAttachments = [];
    for (const attachment of attachments) {
      if (
        attachment["@odata.type"] === "#microsoft.graph.fileAttachment" &&
        !attachment.isInline
      ) {
        const fileExtension = attachment.name.split(".").pop().toLowerCase();

        if (supportedFileTypes.includes(fileExtension)) {
          if (
            fileExtension === "pdf" &&
            attachment.name.toUpperCase().startsWith("PO")
          ) {
            if (poPdfFound) {
              // If a PO PDF has already been found, send a notification
              await sendEmailNotification(
                `Multiple PO PDFs Found: ${attachment.name}`,
                senderEmail || "Unknown Sender",
                new Date().toISOString(),
                accessToken,
                true // Flag for multiple PO PDFs
              );
              logger.info(
                `Notification sent for additional PO PDF: ${attachment.name}`
              );
            } else {
              // If this is the first PO PDF, add it to the list
              downloadedAttachments.push({
                fileName: attachment.name,
                contentType: attachment.contentType,
                contentBytes: attachment.contentBytes, // Base64 encoded
                id: attachment.id,
                size: attachment.size,
                senderEmail: senderEmail,
              });
              poPdfFound = true; // Set the flag to true
              logger.info(`First PO PDF found: ${attachment.name}`);
            }
          } else {
            // Process all other supported files (non-PO PDFs and other file types)
            downloadedAttachments.push({
              fileName: attachment.name,
              contentType: attachment.contentType,
              contentBytes: attachment.contentBytes, // Base64 encoded
              id: attachment.id,
              size: attachment.size,
              senderEmail: senderEmail,
            });
            logger.info(`Processed file: ${attachment.name}`);
          }
        } else {
          // Send notification for unsupported file types
          await sendEmailNotification(
            `Unsupported File Type: ${attachment.name}`,
            senderEmail || "Unknown Sender",
            new Date().toISOString(),
            accessToken,
            true // Flag for unsupported file type
          );
          logger.info(
            `Notification sent for unsupported file type: ${attachment.name}`
          );
        }
      }
    }

    return downloadedAttachments;
  } catch (error) {
    logger.error(`Error fetching email attachments: ${error.message}`);
    throw error;
  }
}

module.exports = {
  uploadToSharePoint,
  createFolderPath,
  getDocumentType,
  getEmailAttachments,
};
