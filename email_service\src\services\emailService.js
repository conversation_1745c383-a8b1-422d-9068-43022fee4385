const { getGraphClient } = require("./authService");
const { sendTeamsNotification, sendEmailNotification } = require("./notificationService");
require("dotenv").config();
const axios = require("axios");
const logger = require("../utils/logger");
const { createQCDigestData } = require("../db");

async function getEmailDetails(messageId, accessToken, mailboxEmailId) {
  try {
    logger.info(`Fetching email details for: ${messageId} from mailbox: ${mailboxEmailId}`);
    
    try {
      const response = await axios.get(
        `https://graph.microsoft.com/v1.0/users/${mailboxEmailId}/messages/${messageId}?$expand=attachments`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      // Check if this is a notification email sent by our system
      const subject = response.data.subject || '';
      const isSystemNotification = subject.includes('No PO PDF File Found') || 
                                subject.includes('Multiple PO Files Detected');
      
      if (isSystemNotification) {
        logger.info(`Skipping processing for system notification email: ${subject}`);
        
        // Mark as read to avoid reprocessing
        await markEmailAsRead(mailboxEmailId, messageId, accessToken);
        
        // Return special object to indicate this email should be skipped
        return { 
          stopReason: 'SYSTEM_NOTIFICATION',
          subject: subject
        };
      }
      // console.log("response",response)
      
      // Check for multiple PO files
      const poFiles = checkForMultiplePOFiles(response.data);
      console.log("poFiles-zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz", poFiles);
      
      if (poFiles.isMultiple) {
        logger.info(`Multiple PO files detected in email: ${messageId}`);
        
        // Send Teams and email notifications about multiple PO files
        await sendMultiplePONotification(response.data, poFiles.files, accessToken);
        
        // Mark email as read to prevent reprocessing
        await markEmailAsRead(mailboxEmailId, messageId, accessToken);
        
        // Return a proper status object instead of null
        return { 
          stopReason: 'MULTIPLE_PO_FILES', 
          emailData: response.data,
          poFiles: poFiles.files
        };
      }
      
      // Check for no PO PDF files
      if (poFiles.files.length === 0) {
        console.log("no po files-zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz");
        
        const result=await createQCDigestData(messageId,response.data.from?.emailAddress?.address,100)
        const digestId=result.DigestID;
        logger.info(`No PO files found in email from ${response.data.from?.emailAddress?.address}. Digest ID: ${digestId}`,{digestId:digestId});
        
        await markEmailAsRead(mailboxEmailId, messageId, accessToken);

        // await sendTeamsNotification(
        //   "No PO Files Found - Processing Stopped",
        //   "WARNING",
        //   {
        //     sender: response.data.from?.emailAddress?.address || 'Unknown Sender',
        //     subject: response.data.subject || 'No Subject',
        //     receivedDateTime: response.data.receivedDateTime,
        //     status: "Stopped",
        //     error: "No PO PDF files found in email",
        //     additionalInfo: {
        //       "DigestID": digestId,
        //       "Action Required": "Please send emails with PO PDF files attached"
        //     }
        //   }
        // );
        
        return { 
          stopReason: 'NO_PO_FILES', 
          emailData: response.data
        };
      }
      
      logger.info(`Successfully fetched email details for: ${messageId}`);
      return response.data;
      
    } catch (error) {
      // Handle 404 errors
      if (error.response && error.response.status === 404) {
        logger.warn(`Email not found: ${messageId} from mailbox: ${mailboxEmailId}. It may have been deleted or moved.`);
        
        // Return a proper status object
        return { 
          stopReason: 'EMAIL_NOT_FOUND',
          messageId, 
          mailboxEmailId 
        };
      }
      
      // Re-throw other errors to be handled by retry mechanism
      throw error;
    }
  } catch (error) {
    logger.error(`Error fetching email details: ${error.message}`);
    throw error;
  }
}

function checkForMultiplePOFiles(emailData) {
  console.log("emaildata.attachments", emailData.attachments,emailData.attachments.length==0)
  if (emailData.attachments.length === 0) return { isMultiple: false, files: [] };

  const poFiles = emailData.attachments.filter(attachment => {
    console.log("attachment.inline",attachment.isInline)
  const fileName = attachment.name?.toLowerCase() || '';
  
  return (
    !attachment.isInline &&
    fileName.includes('po') && 
    fileName.endsWith('.pdf')
  );
});


  return {
    isMultiple: poFiles.length > 1,
    files: poFiles
  };
}

async function sendMultiplePONotification(emailData, poFiles, accessToken) {
  const filesList = poFiles.map(file => file.name);
  const sender = emailData.from?.emailAddress?.address || 'Unknown Sender';
  const subject = emailData.subject || 'No Subject';
  
  // 1. Send Teams notification
  await sendTeamsNotification(
    "️ Multiple PO Files Detected - Processing Stopped",
    "WARNING",
    {
      sender: sender,
      subject: subject,
      receivedDateTime: emailData.receivedDateTime,
      status: "Stopped",
      files: filesList,
      error: "Multiple PO files found in single email",
      additionalInfo: {
        "Total PO Files": poFiles.length
      }
    }
  );
  
  // const ownDomain = 'flrs.co.uk';
  // if (!sender.toLowerCase().endsWith(`@${ownDomain}`)) {
  //   await sendEmailNotification(
  //     "Multiple PO Files Detected in Email",
  //     "warning",
  //     {
  //       sender: sender,
  //       subject: subject,
  //       receivedDateTime: emailData.receivedDateTime,
  //       status: "Processing Stopped",
  //       files: filesList,
  //       error: "Multiple PO files found in a single email",
  //       message: "The document processing system detected multiple PO PDF files in your email. Please send separate emails for each PO file.",
  //       additionalInfo: {
  //         "Total PO Files": poFiles.length,
  //         "Files Detected": filesList.join(', '),
  //         "Requirements": "Please send only one PO PDF file per email",
  //         "Next Steps": "Please send separate emails, each containing a single PO PDF file"
  //       }
  //     },
  //     accessToken
  //   ).catch(error => {
  //     logger.error(`Failed to send email notification for multiple PO files: ${error.message}`);
  //   });
  // }
  
  logger.info(`Sent notifications about multiple PO files in email from ${sender} with subject "${subject}"`);
}

async function markEmailAsRead(mailboxEmailId, messageId, accessToken) {
  try {
    await axios.patch(
      `https://graph.microsoft.com/v1.0/users/${mailboxEmailId}/messages/${messageId}`,
      {
        isRead: true
      },
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    logger.info(`Marked email ${messageId} as read`);
  } catch (error) {
    logger.error(`Error marking email as read: ${error.message}`);
  }
}

module.exports = { getEmailDetails };
