const dotenv = require("dotenv");
const { Client } = require("@microsoft/microsoft-graph-client");
const logger = require("../utils/logger");
const { ConfidentialClientApplication } = require("@azure/msal-node");
dotenv.config();

const msalConfig = {
  auth: {
    clientId: process.env.AZURE_CLIENT_ID,
    clientSecret: process.env.AZURE_CLIENT_SECRET,
    authority: `https://login.microsoftonline.com/${process.env.AZURE_TENANT_ID}`
  }
};

const pca = new ConfidentialClientApplication(msalConfig);

const mailScopes = ['https://graph.microsoft.com/.default'];

async function getAppAccessToken() {
  try {
    logger.info('Acquiring access token using client credentials flow');
    
    const tokenResponse = await pca.acquireTokenByClientCredential({
      scopes: mailScopes
    });
    
    if (!tokenResponse || !tokenResponse.accessToken) {
      throw new Error('Failed to acquire access token');
    }
    
    logger.info('Successfully acquired access token');
    return tokenResponse.accessToken;
  } catch (error) {
    logger.error(`Error acquiring access token: ${error.message}`);
    throw error;
  }
}

async function getGraphClient(accessToken) {
  return Client.init({
    authProvider: (done) => done(null, accessToken),
  });
}

/**
 * Get user ID from webhook event
 * @param {Object} event - Webhook event
 * @returns {string|null} User ID
 */
function getUserIdFromEvent(event) {
  logger.debug('Processing webhook event to extract user ID');
  // Extract user ID from resource path
  // Example: /users/<EMAIL>/messages/123
  const resourcePath = event.resource || '';
  
  // Case-insensitive regex that handles paths with or without leading slash
  const match = resourcePath.match(/\/?[uU]sers\/([^\/]+)\/[mM]essages/);
  
  if (match && match[1]) {
    return match[1];
  }
  
  // Fallback: try to extract from resourceData if available
  if (event.resourceData && event.resourceData['@odata.id']) {
    const odataId = event.resourceData['@odata.id'];
    const odataMatch = odataId.match(/\/?[uU]sers\/([^\/]+)\/[mM]essages/);
    if (odataMatch && odataMatch[1]) {
      return odataMatch[1];
    }
  }
  
  return null;
}

module.exports = {
  getGraphClient,
  pca,
  getAppAccessToken,
  getUserIdFromEvent
};
