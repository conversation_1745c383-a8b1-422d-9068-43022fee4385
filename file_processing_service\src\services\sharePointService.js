const axios = require("axios");
const logger = require("../utils/logger");
const fs = require("fs").promises;
const path = require("path");
const { sendTeamsNotification } = require("../utils/notificationService");

let cachedSiteId = null;

const SHAREPOINT_INVALID_CHARS = /[~"#%&*:<>?/\\{|}]/g;
const SHAREPOINT_INVALID_NAMES = [
  "CON",
  "PRN",
  "AUX",
  "NUL",
  "COM1",
  "COM2",
  "COM3",
  "COM4",
  "COM5",
  "COM6",
  "COM7",
  "COM8",
  "COM9",
  "LPT1",
  "LPT2",
  "LPT3",
  "LPT4",
  "LPT5",
  "LPT6",
  "LPT7",
  "LPT8",
  "LPT9",
];

function validateAndCleanFileName(fileName) {
  if (!fileName) {
    throw new Error("File name cannot be empty");
  }

  // Check for leading/trailing dots and spaces
  if (
    fileName.startsWith(".") ||
    fileName.endsWith(".") ||
    fileName.trim() !== fileName
  ) {
    const errorMessage =
      "Invalid file name: Cannot start or end with dots or contain leading/trailing spaces";
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }

  // Check file name length
  if (fileName.length > 128) {
    const errorMessage = "File name is too long (max 128 characters)";
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }

  // Check for reserved names
  const nameWithoutExt = path.parse(fileName).name.toUpperCase();
  if (SHAREPOINT_INVALID_NAMES.includes(nameWithoutExt)) {
    const errorMessage = `Invalid file name: ${fileName} is a reserved name in SharePoint`;
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }

  // Check for invalid characters
  if (SHAREPOINT_INVALID_CHARS.test(fileName)) {
    const errorMessage = `Invalid characters found in file name: ${fileName}`;
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }

  return fileName;
}

async function getSiteId(accessToken) {
  try {
    if (cachedSiteId) {
      logger.info(`Using cached site ID: ${cachedSiteId}`);
      return cachedSiteId;
    }

    const siteUrl = process.env.SHAREPOINT_SITE_URL;
    logger.info(`Getting site ID for: ${siteUrl}`);

    const urlObj = new URL(siteUrl);
    const hostname = urlObj.hostname;
    const pathParts = urlObj.pathname.split("/").filter(Boolean);
    const siteName = pathParts[1];
    const tenantName = hostname.split(".")[0];

    const formats = [
      `https://graph.microsoft.com/v1.0/sites/${tenantName}.sharepoint.com:/sites/${siteName}`,

      `https://graph.microsoft.com/v1.0/sites/${hostname}:/sites/${siteName}`,
    ];

    let lastError = null;

    for (const endpoint of formats) {
      try {
        logger.info(`Trying endpoint: ${endpoint}`);

        const response = await axios.get(endpoint, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
        });

        logger.info(`Successfully retrieved site ID using: ${endpoint}`);

        cachedSiteId = response.data.id;
        return cachedSiteId;
      } catch (error) {
        lastError = error;
        logger.warn(`Failed with endpoint ${endpoint}: ${error.message}`);
      }
    }

    throw lastError || new Error("All site ID lookup methods failed");
  } catch (error) {
    logger.error(`Error getting site ID: ${error.message}`);
    throw error;
  }
}

async function downloadFileFromSharePoint(
  fileName,
  folderPath,
  accessToken,
  localPath
) {
  try {
    logger.info(
      `Downloading file from SharePoint: ${fileName} from folder: ${folderPath}`
    );

    const siteId = await getSiteId(accessToken);

    // Get file content
    const response = await axios.get(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/drive/root:${folderPath}/${fileName}:/content`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        responseType: "arraybuffer",
      }
    );

    // Ensure local directory exists
    await fs.mkdir(path.dirname(localPath), { recursive: true });

    // Save file locally
    await fs.writeFile(localPath, response.data);

    logger.info(`Successfully downloaded file to: ${localPath}`);
    return localPath;
  } catch (error) {
    logger.error(`Error downloading file from SharePoint: ${error.message}`);
    throw error;
  }
}

async function getSharePointListId(accessToken, listName) {
  try {
    logger.info(`Fetching SharePoint list ID for list: ${listName}`);

    // Get site ID
    const siteId = await getSiteId(accessToken);

    // Fetch all lists from the SharePoint site
    const response = await axios.get(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/lists`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    logger.info(`Retrieved ${response.data.value} lists from SharePoint`);

    // Find the list by name
    const targetList = response.data.value.find(
      (list) => list.name === listName || list.displayName === listName
    );

    if (!targetList) {
      logger.warn(`List with name "${listName}" not found`);

      // Log all available lists to help troubleshoot
      logger.info(
        `Available lists: ${response.data.value
          .map((list) => `${list.displayName} (${list.name})`)
          .join(", ")}`
      );

      throw new Error(`List with name "${listName}" not found`);
    }

    logger.info(
      `Found list: ${targetList.displayName} with ID: ${targetList.id}`
    );
    return targetList.id;
  } catch (error) {
    logger.error(`Error fetching SharePoint list ID: ${error.message}`);
    throw error;
  }
}

async function getSharePointColumns(accessToken) {
  try {
    logger.info("Fetching SharePoint columns");

    // Get site ID
    const siteId = await getSiteId(accessToken);
    console.log("site id", siteId);
    // Get the document library list ID (usually "Documents" or "Shared Documents")
    const listId = await getSharePointListId(accessToken, "Documents");
    console.log("list id", listId);

    // If Documents not found, try Shared Documents
    if (!listId) {
      listId = await getSharePointListId(accessToken, "Shared Documents");
      console.log("list id second attemp", listId);
    }

    logger.info(`Using list ID: ${listId} to fetch columns`);

    // Fetch columns from the SharePoint list
    const response = await axios.get(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/lists/${listId}/columns`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    const columns = response.data.value.map((column) => ({
      displayName: column.displayName,
      internalName: column.name,
    }));
    console.log("columns", columns);
    logger.info(`Successfully fetched ${columns.length} SharePoint columns`);
    return columns;
  } catch (error) {
    logger.error(`Error fetching SharePoint columns: ${error.message}`);
    throw error;
  }
}

async function updateFileMetadata(fileId, accessToken, metadata) {
  try {
    logger.info(
      `Updating metadata for file ID: ${fileId} with metadata: ${JSON.stringify(
        metadata
      )}`
    );

    const siteId = await getSiteId(accessToken);

    const fieldsToUpdate = {
      Category: metadata.Category,
      Country: metadata.Country,
      // Supplier_x0020_Code: metadata.Supplier,
      // Site_x0020_Name: metadata.Company
    };

    if (metadata.Category) fieldsToUpdate.Category = metadata.Category;
    if (metadata.Country) fieldsToUpdate.Country = metadata.Country;
    if (metadata.Supplier)
      fieldsToUpdate.Supplier_x0020_Code = metadata.Supplier;
    if (metadata.Company) fieldsToUpdate.Site_x0020_Name = metadata.Company;

    // Create the combined field if both Category and Country exist
    // if (metadata.Category && metadata.Country) {
    //   fieldsToUpdate.Category_x0020__x002f__x0020_Country = `${metadata.Category} / ${metadata.Country}`;
    // }

    // Log the metadata being sent
    logger.info(`Sending metadata update: ${JSON.stringify(fieldsToUpdate)}`);

    const response = await axios.patch(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/drive/items/${fileId}/listItem/fields`,
      fieldsToUpdate,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    logger.info(`Successfully updated metadata for file ID: ${fileId}`);
    return { success: true, data: response.data };
  } catch (error) {
    logger.error(`Error updating file metadata: ${error.message}`);

    if (error.response) {
      logger.error(`Response status: ${error.response.status}`);
      logger.error(`Response data: ${JSON.stringify(error.response.data)}`);
    }

    return {
      success: false,
      message: error.message,
      error: error.response ? error.response.data : null,
    };
  }
}

async function uploadFileToSharePointWithMetadata(
  fileName,
  filePath,
  folderPath,
  accessToken,
  metadata = {}
) {
  try {
    // First upload the file
    const uploadResponse = await uploadFileToSharePoint(
      fileName,
      filePath,
      folderPath,
      accessToken
    );

    // If metadata is provided and the upload was successful, update the file's metadata
    if (
      metadata &&
      Object.keys(metadata).length > 0 &&
      uploadResponse &&
      uploadResponse.id
    ) {
      await updateFileMetadata(uploadResponse.id, accessToken, metadata);
    }

    return uploadResponse;
  } catch (error) {
    logger.error(`Error uploading file with metadata: ${error.message}`);
    throw error;
  }
}

async function uploadFileToSharePoint(
  fileName,
  filePath,
  folderPath,
  accessToken
) {
  try {
    const validatedFileName = validateAndCleanFileName(fileName);

    logger.info(
      `Uploading file to SharePoint: ${validatedFileName} to folder: ${folderPath}`
    );

    // Get site ID
    const siteId = await getSiteId(accessToken);

    // Ensure folder path exists
    await createFolderPath(folderPath, accessToken);

    // Read file content
    const fileContent = await fs.readFile(filePath);

    // Format the folder path correctly
    const formattedPath = folderPath.startsWith("/")
      ? folderPath.substring(1)
      : folderPath;

    // Upload file using the correct endpoint format for SharePoint
    const response = await axios.put(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/drive/items/root:/${formattedPath}/${validatedFileName}:/content`,
      fileContent,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/octet-stream",
        },
      }
    );

    logger.info(
      `Successfully uploaded file to SharePoint: ${folderPath}/${validatedFileName}`
    );
    return response.data;
  } catch (error) {
    logger.error(`Error uploading file to SharePoint: ${error.message}`);
    throw error;
  }
}

async function createFolderPath(folderPath, accessToken) {
  try {
    logger.info(`Creating folder path: ${folderPath}`);

    // Get site ID
    const siteId = await getSiteId(accessToken);

    // Split path into segments
    const segments = folderPath.split("/").filter((segment) => segment);

    let currentPath = "/Shared Documents";
    let folderInfo = null;

    // Create each folder in the path if it doesn't exist
    for (const segment of segments) {
      try {
        // Check if folder exists
        const checkResponse = await axios.get(
          `https://graph.microsoft.com/v1.0/sites/${siteId}/drive/root:${currentPath}/${segment}`,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "Content-Type": "application/json",
            },
          }
        );

        folderInfo = checkResponse.data;
      } catch (error) {
        // Folder doesn't exist, create it
        if (error.response && error.response.status === 404) {
          logger.info(`Creating folder: ${segment} in ${currentPath}`);

          const createResponse = await axios.post(
            `https://graph.microsoft.com/v1.0/sites/${siteId}/drive/root:${currentPath}:/children`,
            {
              name: segment,
              folder: {},
              "@microsoft.graph.conflictBehavior": "rename",
            },
            {
              headers: {
                Authorization: `Bearer ${accessToken}`,
                "Content-Type": "application/json",
              },
            }
          );

          folderInfo = createResponse.data;
        } else {
          throw error;
        }
      }

      // Update current path
      currentPath = `${currentPath}/${segment}`;
    }

    logger.info(`Successfully created folder path: ${folderPath}`);
    return folderInfo;
  } catch (error) {
    // Add specific error handling for file name validation errors
    if (
      error.message.includes("Invalid file name") ||
      error.message.includes("invalid characters")
    ) {
      logger.error(`SharePoint file name validation error: ${error.message}`);

      // Send Teams notification about invalid file name
      await sendTeamsNotification("Invalid SharePoint File Name", "ERROR", {
        status: "Failed",
        error: error.message,
        additionalInfo: {
          "Original File Name": fileName,
          Issue: "File name contains invalid characters or format",
          "SharePoint Restrictions": [
            "Cannot start or end with dots",
            'Cannot contain ~ " # % & * : < > ? / \\ { | }',
            "Cannot be a reserved name (CON, PRN, AUX, etc.)",
            "Cannot have leading/trailing spaces",
            "Maximum length is 128 characters",
          ].join("\n"),
          "Action Required":
            "Please rename the file following SharePoint naming conventions",
        },
      }).catch((notifyErr) => {
        logger.error(`Failed to send Teams notification: ${notifyErr.message}`);
      });
    }

    logger.error(`Error uploading file to SharePoint: ${error.message}`);
    throw error;
  }
}

async function deleteFileFromSharePoint(fileName, folderPath, accessToken) {
  try {
    logger.info(
      `Deleting file from SharePoint: ${fileName} from folder: ${folderPath}`
    );

    // Get site ID
    const siteId = await getSiteId(accessToken);

    // Delete file
    await axios.delete(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/drive/root:${folderPath}/${fileName}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    logger.info(
      `Successfully deleted file from SharePoint: ${folderPath}/${fileName}`
    );
    return true;
  } catch (error) {
    logger.error(`Error deleting file from SharePoint: ${error.message}`);
    throw error;
  }
}

async function listFilesInFolder(folderPath, accessToken) {
  try {
    logger.info(`Listing files in folder: ${folderPath}`);

    // Get site ID
    const siteId = await getSiteId(accessToken);

    // List files
    const response = await axios.get(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/drive/root:${folderPath}:/children`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    logger.info(
      `Found ${response.data.value.length} items in folder: ${folderPath}`
    );
    return response.data.value;
  } catch (error) {
    logger.error(`Error listing files in folder: ${error.message}`);
    throw error;
  }
}

module.exports = {
  getSiteId,
  downloadFileFromSharePoint,
  uploadFileToSharePoint,
  deleteFileFromSharePoint,
  createFolderPath,
  updateFileMetadata,
  uploadFileToSharePointWithMetadata,
  listFilesInFolder,
  getSharePointListId,
  getSharePointColumns,
};
