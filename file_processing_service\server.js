require('dotenv').config();
const express = require('express');
const logger = require('./src/utils/logger');
const { fileProcessingWorker } = require('./src/workers/queueWorker');
const { startJobScheduler } = require('./src/services/jobScheduler');

const app = express();
app.use(express.json());

const PORT = process.env.PORT || 8009;
app.listen(PORT, async () => {
  console.log(`Server running on port ${PORT}`);
  logger.info(`Server running on port ${PORT}`);
  await startJobScheduler();
});

process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  await fileProcessingWorker.close();
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  await fileProcessingWorker.close();
  process.exit(0);
});
