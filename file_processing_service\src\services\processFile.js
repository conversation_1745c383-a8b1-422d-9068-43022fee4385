const fs = require("fs").promises; // Use fs.promises for async file operations
const path = require("path");
const { performOCR } = require("./ocrProcessing");
const { updateDigestsData, updateDigestProcessingStatus } = require("../db/updateDigests");
const { retryOperation } = require("../utils/retry");
const logger = require("../utils/logger");
const { createFolderStructures } = require("./folderStructure");
const axios = require("axios");
const { getAppAccessToken } = require("./authService");
const {
  downloadFileFromSharePoint,
  uploadFileToSharePointWithMetadata,
  getSiteId,
} = require("./sharePointService");
const { getBusinessUnitByPo } = require("../db/getBusinessUnitByPo");
const {
  sendTeamsNotification,
  sendEmailNotification,
} = require("../utils/notificationService");
const {
  updateDigestFilesProcessingStatus,
} = require("../db/updateDigestFilesProcessingStatus");
const { getBusinessName } = require("../db/getBusinessName");

async function deleteOriginalFile(fileName, digestId, accessToken) {
  try {
    logger.info(
      `Deleting original file ${fileName} from folder Attachments/${digestId}`,
      { digestId: digestId }
    );

    if (!fileName || !digestId || !accessToken) {
      logger.error(
        `Missing required parameters for deletion: fileName=${fileName}, digestId=${digestId}, accessToken=${!!accessToken}`,
        { digestId: digestId }
      );
      return false;
    }

    let siteId = await getSiteId(accessToken);

    const folderPath = `/Attachments/${digestId}`;
    logger.info(`Deleting file ${fileName} from path: ${folderPath}`, {
      digestId: digestId,
    });

    const deleteUrl = `https://graph.microsoft.com/v1.0/sites/${siteId}/drive/root:${folderPath}/${fileName}`;
    logger.info(`DELETE request to: ${deleteUrl}`, { digestId: digestId });

    logger.info(`Using headers: Content-Type: application/json`, {
      digestId: digestId,
    });

    const response = await axios.delete(deleteUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
    });

    logger.info(`Delete response status: ${response.status}`, {
      digestId: digestId,
    });
    logger.info(
      `Deleted original file ${fileName} from SharePoint Attachments/${digestId} folder`,
      { digestId: digestId }
    );
    return true;
  } catch (error) {
    // Log detailed error information
    logger.error(`Error deleting original file ${fileName} from SharePoint:`, {
      digestId: digestId,
    });

    if (error.response) {
      logger.error(`Status: ${error.response.status}`, { digestId: digestId });
      logger.error(`Status Text: ${error.response.statusText}`, {
        digestId: digestId,
      });
      if (error.response.data) {
        logger.error(`Response data: ${JSON.stringify(error.response.data)}`, {
          digestId: digestId,
        });
      }

      // Special handling for common status codes
      if (error.response.status === 401) {
        logger.error(
          `Authentication error: The access token may have expired or is invalid.`,
          { digestId: digestId }
        );
      } else if (error.response.status === 404) {
        logger.error(
          `File not found: The file ${fileName} may not exist at the specified path.`,
          { digestId: digestId }
        );
      } else if (error.response.status === 403) {
        logger.error(
          `Permission denied: The app may not have delete permissions.`,
          { digestId: digestId }
        );
      }
    } else {
      logger.error(`Error message: ${error.message}`, { digestId: digestId });
    }

    // Don't throw here - we don't want to fail the whole process if deletion fails
    return false;
  }
}

async function deleteDigestFolder(
  digestId,
  accessToken,
  isValidationFailed = false
) {
  try {
    if (!digestId || !accessToken) {
      logger.error(
        `Missing required parameters for folder deletion: digestId=${digestId}, accessToken=${!!accessToken}`,
        { digestId: digestId }
      );
      return false;
    }

    logger.info(`Attempting to delete digest folder: Attachments/${digestId}`, {
      digestId: digestId,
    });

    let siteId = await getSiteId(accessToken);

    const folderPath = `/Attachments/${digestId}`;

    // Check if the folder exists and is empty
    try {
      logger.info(`Checking if folder ${folderPath} is empty`, {
        digestId: digestId,
      });

      const folderContentsResponse = await axios.get(
        `https://graph.microsoft.com/v1.0/sites/${siteId}/drive/root:${folderPath}:/children`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      const folderContents = folderContentsResponse.data.value;

      if (folderContents.length > 0 && !isValidationFailed) {
        logger.warn(
          `Folder ${folderPath} still contains ${folderContents.length} items, skipping deletion`,
          { digestId: digestId }
        );
        // Log the items left in the folder for debugging
        folderContents.forEach((item, index) => {
          logger.warn(`Item ${index + 1}: ${item.name} (${item.id})`, {
            digestId: digestId,
          });
        });
        return false;
      }

      // If we get here, the folder exists and is empty
      logger.info(`Folder ${folderPath} is empty, proceeding with deletion`, {
        digestId: digestId,
      });
    } catch (error) {
      // If getting folder contents fails with 404, folder doesn't exist
      if (error.response && error.response.status === 404) {
        logger.info(`Folder ${folderPath} does not exist, nothing to delete`, {
          digestId: digestId,
        });
        return true;
      }

      // For other errors, log and continue with deletion attempt
      logger.warn(
        `Error checking folder contents: ${error.message}. Attempting deletion anyway.`,
        { digestId: digestId }
      );
    }

    // Delete the folder
    try {
      logger.info(`Deleting folder: ${folderPath}`, { digestId: digestId });

      const deleteResponse = await axios.delete(
        `https://graph.microsoft.com/v1.0/sites/${siteId}/drive/root:${folderPath}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      logger.info(`Successfully deleted digest folder: ${folderPath}`, {
        digestId: digestId,
      });
      return true;
    } catch (error) {
      if (error.response) {
        if (error.response.status === 404) {
          logger.warn(
            `Folder ${folderPath} not found, may have been already deleted`
          );
          return true;
        }

        logger.error(`Error deleting folder: Status ${error.response.status}`, {
          digestId: digestId,
        });
        if (error.response.data) {
          logger.error(
            `Error details: ${JSON.stringify(error.response.data)}`,
            { digestId: digestId }
          );
        }
      } else {
        logger.error(`Error deleting folder: ${error.message}`, {
          digestId: digestId,
        });
      }

      return false;
    }
  } catch (error) {
    logger.error(`Error in deleteDigestFolder: ${error.message}`, {
      digestId: digestId,
    });
    return false;
  }
}

const processFile = async (file, docType2Files = [], docType3Files = []) => {
  if (!file || !file.FileName) {
    throw new Error("Invalid file data provided");
  }
  const digestId = file.DigestID;

  let filePath = null;
  let jsonFilePath = null;
  const downloadedFiles = []; // Track all downloaded files for cleanup

  console.log("Processing file:", file.FileName);
  console.log("Document Type 2 files:", docType2Files.length);
  console.log("Document Type 3 files:", docType3Files.length);

  try {
    logger.info(
      `Starting to process file: ${file.FileName} (with ${docType2Files.length} sensor readings and ${docType3Files.length} related files)`,
      { digestId: digestId }
    );

    const accessToken = await getAppAccessToken();

    // Create a folder based on digestId
    console.log("digestId", digestId);
    const tempDir = path.join(
      __dirname,
      "../../downloads",
      digestId.toString()
    );
    const localFilePath = path.join(tempDir, file.FileName);

    // Ensure temp directory exists
    await fs.mkdir(tempDir, { recursive: true });

    // Fetch file from SharePoint with retry
    filePath = await retryOperation(
      () =>
        downloadFileFromSharePoint(
          file.FileName,
          "/Attachments/" + digestId, // Source folder in SharePoint
          accessToken,
          localFilePath
        ),
      "fetchFileFromSharePoint"
    );
    downloadedFiles.push(filePath); // Add to tracking list
    logger.info(`Successfully downloaded file to: ${filePath}`, {
      digestId: digestId,
    });

    // Only process document type 1 with OCR
    const ocrResult = await performOCR(filePath, digestId);

    if (!ocrResult || !ocrResult.parsedData) {
      throw new Error("OCR processing failed to return valid data");
    }

    const { parsedData, jsonFilePath: ocrJsonPath } = ocrResult;
    jsonFilePath = ocrJsonPath;

    if (!parsedData.pages?.[4]?.data?.customer_po) {
      const result=await updateDigestProcessingStatus(digestId, 100);
      if(result.message==="Failed"){
        throw new Error("Failed to update digest processing status");
      }
      const errorMessage =
        "Unable to find customer_po in OCR results - Possible incorrect document type";
      logger.error(`${errorMessage} for file: ${filePath}`);

      // Send detailed Teams notification
      await sendTeamsNotification("Document Structure Mismatch", "ERROR", {
        status: "Failed",
        error: errorMessage,
        additionalInfo: {
          "File Path": filePath,
          "Digest ID": digestId,
          "Expected Location": "pages[4].data.customer_po",
          "Available Pages": parsedData.pages ? parsedData.pages.length : 0,
          "Page 4 Structure": parsedData.pages?.[4]
            ? `Available fields: ${Object.keys(
                parsedData.pages[4].data || {}
              ).join(", ")}`
            : "Page 4 not available",
          "OCR JSON Path": ocrJsonPath,
          "Document Analysis":
            "Document structure does not match expected template format",
        },
      }).catch((notifyErr) => {
        logger.error(
          `Failed to send error notification to Teams: ${notifyErr.message}`
        );
      });

      throw new Error(errorMessage);
    }

    // Extract data from OCR result
    const poNumber = parsedData.pages[4].data.customer_po;

    // Get business unit data and validate customer
    const businessUnitData = await getBusinessUnitByPo(poNumber);
    const senderEmail = file.SenderEmail;

    // Extract domain (company name) from sender's email
    let senderDomain = "unknown";
    if (senderEmail && typeof senderEmail === "string") {
      // Extract everything after the @ symbol
      const atIndex = senderEmail.lastIndexOf("@");
      if (atIndex !== -1 && atIndex < senderEmail.length - 1) {
        const fullDomain = senderEmail.substring(atIndex + 1);

        const domainParts = fullDomain.split(".");

        if (domainParts.length > 0) {
          senderDomain = domainParts[0];

          if (domainParts.length >= 3 && domainParts[1] === "co") {
            senderDomain = domainParts[0];
          }
        }
      }
    }

    logger.info(`Sender email domain: ${senderDomain}`, { digestId: digestId });
    const businessName = await getBusinessName(senderDomain);
    console.log("businessUnit", businessUnitData);

    // Get end customer from OCR
    const endCustomer =
      parsedData.pages[4].data.end_customer || "Unknown-Customer";

    // Validate customer information
    let validationErrors = [];

    // Check if business unit data exists and customer is valid
    if (!businessUnitData) {
      validationErrors.push({
        type: "Missing Business Unit",
        message: `No business unit data found for PO: ${poNumber}`,
        details: "PO number not found in the system",
      });
    } else {
      // Check IsValidCustomer flag
      if (!businessUnitData.IsValidCustomer) {
        validationErrors.push({
          type: "Invalid Customer",
          message: `Customer is marked as invalid in the system`,
          details: `Business Unit: ${businessUnitData.BusinessUnit}, Customer: ${businessUnitData.CustomerName}`,
        });
      }

      // Check if CustomerName in database is NULL/NO
      if (
        !businessUnitData.CustomerName ||
        businessUnitData.CustomerName === "NULL/NO"
      ) {
        validationErrors.push({
          type: "Invalid Customer Name in Database",
          message: "Customer name is NULL/NO in the database",
          details: `PO: ${poNumber}, Business Unit: ${businessUnitData.BusinessUnit}`,
        });
      }
    }

    // Check end customer from OCR
    if (!endCustomer || endCustomer === "NULL/NO") {
      validationErrors.push({
        type: "Missing End Customer in Document",
        message:
          "End customer information is missing or invalid in the document",
        details: `End customer value from OCR: ${endCustomer}`,
      });
    }

    // If there are validation errors, send notifications and stop processing
    if (validationErrors.length > 0) {
      const result=await updateDigestProcessingStatus(digestId, 100);
      if(result.message==="Failed"){
        throw new Error("Failed to update digest processing status");
      }
      logger.error(
        `Validation errors found for file ${file.FileName}:${validationErrors}`,
        { digestId: digestId }
      );

      // Send Teams notification
      await sendTeamsNotification(
        `INVALID CUSTOMER - File "${file.FileName}"`,
        "WARNING",
        {
          status: "Processing Stopped",
          error: validationErrors.map((err) => err.type).join(", "),
          additionalInfo: {
            "File Name": file.FileName,
            "PO Number": poNumber,
            "End Customer (Document)": endCustomer,
            "Customer Name (Database)":
              businessUnitData?.CustomerName || "Not Found",
            "Business Unit": businessUnitData?.BusinessUnit || "Not Found",
            Country: businessUnitData?.Country || "Not Found",
            "Validation Errors": validationErrors
              .map((err) => `${err.type}: ${err.message}`)
              .join("\n"),
            "Action Required":
              "Please verify customer details in both the document and database",
          },
        }
      );

      // Send email notification
      // const ownDomain = "flrs.co.uk";
      // if (!file.senderEmail?.toLowerCase().endsWith(`@${ownDomain}`)) {
      //   await sendEmailNotification(
      //     `INVALID CUSTOMER - File "${file.FileName}"`,
      //     "error",
      //     {
      //       fileName: file.FileName,
      //       poNumber: poNumber,
      //       status: "Processing Stopped",
      //       error: validationErrors.map((err) => err.type).join(", "),
      //       message: `The document "${file.FileName}" processing has been stopped due to customer validation issues.`,
      //       additionalInfo: {
      //         "File Name": file.FileName,
      //         "End Customer (Document)": endCustomer,
      //         "Customer Name (Database)":
      //           businessUnitData?.CustomerName || "Not Found",
      //         "Business Unit": businessUnitData?.BusinessUnit || "Not Found",
      //         Country: businessUnitData?.Country || "Not Found",
      //         "Validation Errors": validationErrors
      //           .map((err) => `${err.type}: ${err.message}`)
      //           .join("\n"),
      //         "Required Action":
      //           "Please ensure valid customer information is provided in both the document and database",
      //       },
      //     },
      //     accessToken
      //   );
      // }

      // Update digest data with parsed OCR data and status 100
      await retryOperation(
        () => updateDigestsData(file, 100, parsedData), // Status 100 for validation failure
        "updateDigestsData_ValidationFailed"
      ).catch((error) => {
        logger.error(
          `Failed to update digest data after validation failure: ${error.message}`,
          { digestId: digestId }
        );
      });

      // Delete the original file...
      logger.info(
        `Deleting original file ${file.FileName} from SharePoint due to validation failure`,
        { digestId: digestId }
      );
      await retryOperation(
        () => deleteDigestFolder(digestId, accessToken, true),
        "deleteDigestFolder_ValidationFailed"
      ).catch((error) => {
        logger.error(
          `Failed to delete digest folder from SharePoint after validation failure: ${error.message}`,
          { digestId: digestId }
        );
      });

      // Cleanup files...
      await cleanupFiles(filePath, jsonFilePath, digestId);
      for (const downloadedFile of downloadedFiles) {
        if (downloadedFile !== filePath) {
          await fs.unlink(downloadedFile).catch(() => {});
        }
      }

      // Update processing status
      await updateDigestFilesProcessingStatus(100, file.ID);

      return {
        status: "validation_failed",
        message: `INVALID CUSTOMER - ${validationErrors
          .map((err) => err.type)
          .join(", ")}`,
        errors: validationErrors,
        fileName: file.FileName,
        fileDeleted: true,
      };
    }

    const supplierCode =
      parsedData.pages[4].data.supplier_code ||
      parsedData.pages[4].data.supplier;

    if (!supplierCode) {
      const errorMessage = "OCR failed to extract Supplier Code";
      logger.error(`${errorMessage} for file: ${filePath}`);
      const result=await updateDigestProcessingStatus(digestId, 100);
      if(result.message==="Failed"){
        throw new Error("Failed to update digest processing status");
      }
      await sendTeamsNotification(
        "Missing Supplier Code in Document",
        "ERROR",
        {
          status: "Failed",
          error: errorMessage,
          additionalInfo: {
            "File Path": filePath,
            "Digest ID": digestId,
            "Document Type": "QC Report",
            "Expected Location":
              "pages[4].data.supplier_code or pages[4].data.supplier",
            "Available Fields": parsedData.pages?.[4]?.data
              ? Object.keys(parsedData.pages[4].data).join(", ")
              : "No data available on page 4",
            "OCR JSON Path": ocrJsonPath,
            "Action Required":
              "Please verify the document contains a valid Supplier Code",
          },
        }
      );

      throw new Error(errorMessage);
    }

    if (supplierCode.includes("¬")) {
      const result=await updateDigestProcessingStatus(digestId, 100);
      if(result.message==="Failed"){
        throw new Error("Failed to update digest processing status");
      }
      const errorMessage =
        "OCR returned invalid character ('¬') in Supplier Code - Template may not be correctly configured";
      logger.error(`${errorMessage} for file: ${filePath}`);

      await sendTeamsNotification("Invalid Supplier Code Format", "ERROR", {
        status: "Failed",
        error: errorMessage,
        additionalInfo: {
          "File Path": filePath,
          "Digest ID": digestId,
          "Document Type": "QC Report",
          "Extracted Supplier Code": supplierCode,
          Issue: "Special character '¬' detected in supplier code",
          "Possible Cause":
            "OCR template may not be correctly configured for this document type",
          "Action Required":
            "Please review and update the OCR template configuration",
          "Technical Note":
            "This usually indicates the Supplier Code field in Prophet ISS was not properly set up before the QC report was generated",
        },
      });

      throw new Error(errorMessage);
    }

    const datePatterns = [
      /^\d{1,2}[-/\.]\d{1,2}[-/\.]\d{2,4}$/, // matches dd/mm/yyyy, dd-mm-yyyy, dd.mm.yyyy
      /^\d{2,4}[-/\.]\d{1,2}[-/\.]\d{1,2}$/, // matches yyyy/mm/dd, yyyy-mm-dd, yyyy.mm.dd
      /^\d{1,2}[-/\.][A-Za-z]{3}[-/\.]\d{2,4}$/, // matches dd-MMM-yyyy, dd/MMM/yyyy
      /^\d{8}$/, // matches DDMMYYYY or YYYYMMDD
    ];

    const isDateFormat = datePatterns.some((pattern) =>
      pattern.test(supplierCode)
    );

    if (isDateFormat) {
      const errorMessage =
        "OCR appears to have extracted a date instead of Supplier Code";
      logger.error(`${errorMessage} for file: ${filePath}`);
      const result=await updateDigestProcessingStatus(digestId, 100);
      if(result.message==="Failed"){
        throw new Error("Failed to update digest processing status");
      }
      await sendTeamsNotification(
        "Date Format Detected in Supplier Code",
        "ERROR",
        {
          status: "Failed",
          error: errorMessage,
          additionalInfo: {
            "File Path": filePath,
            "Digest ID": digestId,
            "Document Type": "QC Report",
            "Extracted Value": supplierCode,
            Issue: "Date format detected where Supplier Code should be",
            "Possible Cause":
              "OCR may be reading from wrong field or template configuration issue",
            "Action Required":
              "Please verify the correct location of Supplier Code in the document",
            "Technical Details": {
              Expected:
                "Valid supplier code format (letters, numbers, hyphens, underscores)",
              Received: "Date-like format",
              Location: "pages[4].data.supplier_code or pages[4].data.supplier",
            },
          },
        }
      );

      throw new Error(errorMessage);
    }

    const category = parsedData.pages[0].data.category || "Unknown-Category";
    console.log("category", category);
    const inspectionDate =
      parsedData.pages[4].data.inspection_date ||
      parsedData.pages[1].data.inspection_date ||
      new Date().toISOString().split("T")[0];

    const qcIntakeFolderInfo = await retryOperation(
      () =>
        createFolderStructures(
          poNumber,
          supplierCode,
          endCustomer,
          category,
          inspectionDate,
          accessToken,
          1,
          businessName,
          digestId
        ),
      "createQCIntakeFolderStructures"
    );

    const sensorReadingsFolderInfo = await retryOperation(
      () =>
        createFolderStructures(
          poNumber,
          supplierCode,
          endCustomer,
          category,
          inspectionDate,
          accessToken,
          2,
          businessName,
          digestId
        ),
      "createSensorReadingsFolderStructures"
    );

    // Prepare metadata for SharePoint columns
    console.log(
      "this is to check why category is not being added",
      parsedData.pages[1].data.category
    );
    const metadata = {
      Category: parsedData.pages[0].data.category || "General",
      Country: parsedData.pages[4].data.coo || "Unknown",
      Supplier: supplierCode,
      Company: endCustomer,
      // "Category/Country": `${
      //   parsedData.pages[4].data.category || "General"
      // } / ${parsedData.pages[4].data.coo || "Unknown"}`,
    };

    logger.info(
      `Preparing to upload file with metadata: ${JSON.stringify(metadata)}`,
      { digestId: digestId }
    );

    await retryOperation(
      () =>
        uploadFileToSharePointWithMetadata(
          file.FileName,
          filePath,
          qcIntakeFolderInfo.supplierPath,
          accessToken,
          metadata
        ),
      "uploadFileToSupplierPath"
    );

    await retryOperation(
      () =>
        uploadFileToSharePointWithMetadata(
          file.FileName,
          filePath,
          qcIntakeFolderInfo.customerPath,
          accessToken,
          metadata
        ),
      "uploadFileToCustomerPath"
    );

    for (const docType2File of docType2Files) {
      try {
        logger.info(
          `Processing sensor reading file: ${docType2File.FileName}`,
          { digestId: digestId }
        );

        const docType2LocalPath = path.join(tempDir, docType2File.FileName);
        const downloadedDocType2Path = await retryOperation(
          () =>
            downloadFileFromSharePoint(
              docType2File.FileName,
              "/Attachments/" + docType2File.DigestID,
              accessToken,
              docType2LocalPath
            ),
          "downloadDocType2File"
        );

        downloadedFiles.push(downloadedDocType2Path);

        await retryOperation(
          () =>
            uploadFileToSharePointWithMetadata(
              docType2File.FileName,
              downloadedDocType2Path,
              sensorReadingsFolderInfo.supplierPath,
              accessToken,
              metadata
            ),
          "uploadDocType2ToSupplierPath"
        );

        await retryOperation(
          () =>
            uploadFileToSharePointWithMetadata(
              docType2File.FileName,
              downloadedDocType2Path,
              sensorReadingsFolderInfo.customerPath,
              accessToken,
              metadata
            ),
          "uploadDocType2ToCustomerPath"
        );

        await retryOperation(
          () =>
            deleteOriginalFile(
              docType2File.FileName,
              docType2File.DigestID,
              accessToken
            ),
          "deleteOriginalDocType2File"
        );

        logger.info(
          `Successfully processed sensor reading file: ${docType2File.FileName}`,
          { digestId: digestId }
        );
      } catch (error) {
        logger.error(
          `Error processing sensor reading file ${docType2File.FileName}:${error.message}`,
          { digestId: digestId }
        );
      }
    }

    for (const docType3File of docType3Files) {
      try {
        logger.info(
          `Processing related document type 3 file: ${docType3File.FileName}`,
          { digestId: digestId }
        );

        const docType3LocalPath = path.join(tempDir, docType3File.FileName);
        const downloadedDocType3Path = await retryOperation(
          () =>
            downloadFileFromSharePoint(
              docType3File.FileName,
              "/Attachments/" + docType3File.DigestID,
              accessToken,
              docType3LocalPath
            ),
          "downloadDocType3File"
        );

        downloadedFiles.push(downloadedDocType3Path);

        await retryOperation(
          () =>
            uploadFileToSharePointWithMetadata(
              docType3File.FileName,
              downloadedDocType3Path,
              qcIntakeFolderInfo.supplierPath,
              accessToken,
              metadata
            ),
          "uploadDocType3ToSupplierPath"
        );

        await retryOperation(
          () =>
            uploadFileToSharePointWithMetadata(
              docType3File.FileName,
              downloadedDocType3Path,
              qcIntakeFolderInfo.customerPath,
              accessToken,
              metadata
            ),
          "uploadDocType3ToCustomerPath"
        );

        await retryOperation(
          () =>
            deleteOriginalFile(
              docType3File.FileName,
              docType3File.DigestID,
              accessToken
            ),
          "deleteOriginalDocType3File"
        );

        logger.info(
          `Successfully processed document type 3 file: ${docType3File.FileName}`,
          { digestId: digestId }
        );
      } catch (error) {
        logger.error(
          `Error processing document type 3 file ${docType3File.FileName}:${error.message}`,
          { digestId: digestId }
        );
      }
    }

    const updateResult = await retryOperation(
      () => updateDigestsData(file, 200, parsedData),
      "updateDigestsData"
    );

    if (updateResult.message === "Success") {
      await retryOperation(
        () => deleteOriginalFile(file.FileName, file.DigestID, accessToken),
        "deleteOriginalFile"
      );

      await cleanupFiles(filePath, jsonFilePath);

      for (const downloadedFile of downloadedFiles) {
        if (downloadedFile !== filePath) {
          await fs
            .unlink(downloadedFile)
            .catch((err) =>
              logger.error(
                `Error deleting additional file ${downloadedFile}:${err}`,
                { digestId: digestId }
              )
            );
        }
      }

      try {
        const remainingFiles = await fs.readdir(tempDir);
        if (remainingFiles.length === 0) {
          await fs.rmdir(tempDir);
          logger.info(`Removed empty local digest folder: ${tempDir}`, {
            digestId: digestId,
          });
        }
      } catch (error) {
        logger.error(
          `Error cleaning up local digest folder ${tempDir}:${error.message}`,
          { digestId: digestId }
        );
      }

      try {
        logger.info(
          `Attempting to clean up SharePoint digest folder: ${digestId}`,
          { digestId: digestId }
        );

        const freshToken = await getAppAccessToken();

        const folderDeleted = await retryOperation(
          () => deleteDigestFolder(digestId, freshToken),
          "deleteDigestFolder"
        );

        if (folderDeleted) {
          logger.info(
            `Successfully deleted SharePoint digest folder: Attachments/${digestId}`,
            { digestId: digestId }
          );
        } else {
          logger.warn(
            `Failed to delete SharePoint digest folder: Attachments/${digestId} - it may not be empty or might not exist`,
            { digestId: digestId }
          );
        }
      } catch (error) {
        logger.error(
          `Error deleting SharePoint digest folder: ${error.message}`,
          { digestId: digestId }
        );
      }

      logger.info(
        `Successfully processed file: ${file.FileName} with ${docType2Files.length} sensor readings and ${docType3Files.length} related files`,
        { digestId: digestId }
      );

      return {
        status: "success",
        message: `Successfully processed file: ${file.FileName}`,
        details: {
          fileName: file.FileName,
          poNumber: poNumber,
          sensorReadingsCount: docType2Files.length,
          relatedFilesCount: docType3Files.length,
          businessUnit: businessUnitData?.BusinessUnit,
          customer: endCustomer,
          sharepointCleanup: true,
        },
      };
    } else {
      return {
        status: "failed",
        message: `Database update failed for file: ${file.FileName}`,
        error: updateResult.error || "Unknown error during database update",
        fileName: file.FileName,
      };
    }
  } catch (error) {
    logger.error(`Failed to process file: ${file.FileName}:${error}`, {
      digestId: digestId,
    });
    
    // Clean up only local files
    await cleanupFiles(filePath, jsonFilePath).catch((cleanupError) => {
      logger.error(`Failed to cleanup local files:${cleanupError}`, {
        digestId: digestId,
      });
    });

    // Clean up other downloaded files except the original
    for (const downloadedFile of downloadedFiles) {
      if (downloadedFile !== filePath) {
        await fs.unlink(downloadedFile).catch(() => {});
      }
    }

    // Do NOT delete SharePoint files on error
    // Instead, just log the error and return failure status
    return {
      status: "error",
      message: `Processing failed for file: ${file.FileName}`,
      error: error.message,
      fileName: file.FileName,
      stack: error.stack,
      // Add flag to indicate file was preserved for retry
      filePreserved: true
    };
  }
};

const cleanupFiles = async (filePath, jsonFilePath, digestId) => {
  try {
    if (
      filePath &&
      (await fs
        .access(filePath)
        .then(() => true)
        .catch(() => false))
    ) {
      await fs.unlink(filePath);
      logger.info(`Deleted file: ${filePath}`, { digestId: digestId });
    }

    if (
      jsonFilePath &&
      (await fs
        .access(jsonFilePath)
        .then(() => true)
        .catch(() => false))
    ) {
      await fs.unlink(jsonFilePath);
      logger.info(`Deleted JSON file: ${jsonFilePath}`, { digestId: digestId });
    }
  } catch (error) {
    logger.error(`Error during file cleanup:${error}`, { digestId: digestId });
    throw error;
  }
};

module.exports = { processFile, deleteOriginalFile, deleteDigestFolder };
