const logger = require('../utils/logger');
const { createSubscription, listSubscriptions } = require('./subscriptionService');
const { getActiveMailboxes } = require('../db/mailboxService');

async function initSubscriptionManager() {
  try {
    logger.info('Initializing subscription manager for all mailboxes');
    
    // Get all active mailboxes from database
    const mailboxes = await getActiveMailboxes();
    logger.info(`Found ${mailboxes.length} active mailboxes to subscribe`);
    
    if (mailboxes.length === 0) {
      logger.warn('No active mailboxes found in database. Subscription manager initialization skipped.');
      return;
    }
    
    // First check existing subscriptions to avoid duplicates
    const existingSubscriptions = await listSubscriptions();
    logger.info(`Current subscriptions (${existingSubscriptions.length}):`);
    existingSubscriptions.forEach(sub => {
      logger.info(`- ID: ${sub.id}, Resource: ${sub.resource}, Expires: ${sub.expirationDateTime}`);
    });

    // Create a quick lookup by email (lowercased)
    const mailboxMap = {};
    mailboxes.forEach(mailbox => {
      mailboxMap[mailbox.email.toLowerCase()] = mailbox;
    });

    const results = {
      success: [],
      failed: [],
      duplicates: []
    };

    // Track emails that already have subscriptions
    const emailsWithSubs = new Set();

    // Identify which mailboxes already have subscriptions
    existingSubscriptions.forEach(sub => {
      const match = sub.resource.match(/users\/([^\/]+)\/messages/i);
      if (match) {
        const identifier = match[1].toLowerCase();
        if (mailboxMap[identifier]) {
          const mailbox = mailboxMap[identifier];
          emailsWithSubs.add(mailbox.email.toLowerCase());
          results.duplicates.push({
            email: mailbox.email,
            subscriptionId: sub.id,
            resourcePath: sub.resource
          });
        }
      }
    });

    // Create subscriptions for mailboxes that don't have one
    for (const mailbox of mailboxes) {
      try {
        if (emailsWithSubs.has(mailbox.email.toLowerCase())) {
          logger.info(`Subscription already exists for mailbox: ${mailbox.email}`);
          continue;
        }

        // Create subscription using the email
        const subscription = await createSubscription(mailbox.email);

        logger.info(`Created subscription for mailbox: ${mailbox.email}`);
        results.success.push({
          email: mailbox.email,
          subscriptionId: subscription.id
        });

        emailsWithSubs.add(mailbox.email.toLowerCase());
      } catch (error) {
        logger.error(`Failed to create subscription for mailbox ${mailbox.email}: ${error.message}`);
        results.failed.push({
          email: mailbox.email,
          error: error.message
        });
      }
    }

    logger.info(`Subscription initialization complete. Created: ${results.success.length}, Failed: ${results.failed.length}, Already exists: ${results.duplicates.length}`);
    
    return results;
  } catch (error) {
    logger.error(`Failed to initialize subscription manager: ${error.message}`);
    throw error;
  }
}


module.exports = {
  initSubscriptionManager,
}; 